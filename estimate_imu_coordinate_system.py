#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IMU坐标系估计脚本

功能：
- 分析无人机运动数据，估计IMU坐标系方向
- 基于运动轨迹、加速度数据和初速度假设进行分析
- 判断IMU坐标系是前右下、上右前、前左上等哪种配置

假设条件：
- 无人机有一个轴向前的10m/s初速度
- 其他两个轴的初速度为0
- 无人机向下同时向前飞行

"""

import numpy as np
import matplotlib.pyplot as plt
import csv
import math
from pathlib import Path

def load_imu_data(file_path):
    """
    加载IMU数据
    """
    data = []
    with open(file_path, 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        header = next(reader)  # 跳过标题行
        
        for row in reader:
            if len(row) >= 14:
                try:
                    frame_id = int(row[0])
                    lon = float(row[1])
                    lat = float(row[2])
                    alt = float(row[3])
                    pitch = float(row[4])  # 俯仰角
                    roll = float(row[5])   # 滚转角
                    yaw = float(row[6])    # 偏航角
                    acc_x = float(row[8])  # 加速度X (g)
                    acc_y = float(row[9])  # 加速度Y (g)
                    acc_z = float(row[10]) # 加速度Z (g)
                    gyro_x = float(row[11]) # 角速度X (dps)
                    gyro_y = float(row[12]) # 角速度Y (dps)
                    gyro_z = float(row[13]) # 角速度Z (dps)
                    
                    data.append({
                        'frame': frame_id,
                        'lon': lon, 'lat': lat, 'alt': alt,
                        'pitch': pitch, 'roll': roll, 'yaw': yaw,
                        'acc_x': acc_x, 'acc_y': acc_y, 'acc_z': acc_z,
                        'gyro_x': gyro_x, 'gyro_y': gyro_y, 'gyro_z': gyro_z
                    })
                except ValueError:
                    continue
    
    return data

def convert_gps_to_meters(data):
    """
    将GPS坐标转换为米制坐标（相对于起始点）
    """
    if not data:
        return data
    
    # 地球半径（米）
    R = 6378137.0
    
    # 起始点
    lat0 = math.radians(data[0]['lat'])
    lon0 = math.radians(data[0]['lon'])
    alt0 = data[0]['alt']
    
    for i, point in enumerate(data):
        lat = math.radians(point['lat'])
        lon = math.radians(point['lon'])
        alt = point['alt']
        
        # 计算相对位移（米）
        dx = R * (lon - lon0) * math.cos(lat0)  # 东向位移
        dy = R * (lat - lat0)                   # 北向位移
        dz = alt - alt0                         # 高度位移
        
        data[i]['x_enu'] = dx  # 东
        data[i]['y_enu'] = dy  # 北
        data[i]['z_enu'] = dz  # 上
    
    return data

def analyze_motion_direction(data):
    """
    分析主要运动方向
    """
    if len(data) < 2:
        return None
    
    # 计算总位移
    total_dx = data[-1]['x_enu'] - data[0]['x_enu']
    total_dy = data[-1]['y_enu'] - data[0]['y_enu']
    total_dz = data[-1]['z_enu'] - data[0]['z_enu']
    
    # 计算运动方向角度（相对于东北天坐标系）
    horizontal_distance = math.sqrt(total_dx**2 + total_dy**2)
    total_distance = math.sqrt(total_dx**2 + total_dy**2 + total_dz**2)
    
    # 水平方向角度（从东向北为正）
    horizontal_angle = math.degrees(math.atan2(total_dy, total_dx))
    
    # 俯仰角度（向下为负）
    elevation_angle = math.degrees(math.atan2(total_dz, horizontal_distance))
    
    print(f"运动分析:")
    print(f"  总位移: 东{total_dx:.2f}m, 北{total_dy:.2f}m, 上{total_dz:.2f}m")
    print(f"  水平距离: {horizontal_distance:.2f}m")
    print(f"  总距离: {total_distance:.2f}m")
    print(f"  水平方向角: {horizontal_angle:.1f}° (东向为0°)")
    print(f"  俯仰角: {elevation_angle:.1f}° (向下为负)")
    
    return {
        'total_displacement': [total_dx, total_dy, total_dz],
        'horizontal_angle': horizontal_angle,
        'elevation_angle': elevation_angle,
        'total_distance': total_distance
    }

def integrate_acceleration(data, dt=1.0):
    """
    对加速度进行积分得到速度和位移
    """
    # 转换单位：g -> m/s²
    G = 9.80665
    
    for i, point in enumerate(data):
        # 转换加速度单位
        point['acc_x_ms2'] = point['acc_x'] * G
        point['acc_y_ms2'] = point['acc_y'] * G
        point['acc_z_ms2'] = point['acc_z'] * G
        
        if i == 0:
            # 初始速度假设：一个轴10m/s，其他轴0
            point['vel_x'] = 0  # 待确定
            point['vel_y'] = 0  # 待确定
            point['vel_z'] = 0  # 待确定
            point['pos_x'] = 0
            point['pos_y'] = 0
            point['pos_z'] = 0
        else:
            # 积分计算速度和位移
            prev = data[i-1]
            point['vel_x'] = prev['vel_x'] + point['acc_x_ms2'] * dt
            point['vel_y'] = prev['vel_y'] + point['acc_y_ms2'] * dt
            point['vel_z'] = prev['vel_z'] + point['acc_z_ms2'] * dt
            
            point['pos_x'] = prev['pos_x'] + prev['vel_x'] * dt + 0.5 * point['acc_x_ms2'] * dt**2
            point['pos_y'] = prev['pos_y'] + prev['vel_y'] * dt + 0.5 * point['acc_y_ms2'] * dt**2
            point['pos_z'] = prev['pos_z'] + prev['vel_z'] * dt + 0.5 * point['acc_z_ms2'] * dt**2
    
    return data

def test_coordinate_systems(data, motion_info):
    """
    测试不同的坐标系配置
    """
    # 定义可能的坐标系配置
    # 格式：[X轴方向, Y轴方向, Z轴方向]
    coordinate_systems = {
        '前右下': ['forward', 'right', 'down'],
        '前左下': ['forward', 'left', 'down'],
        '前右上': ['forward', 'right', 'up'],
        '前左上': ['forward', 'left', 'up'],
        '后右下': ['backward', 'right', 'down'],
        '后左下': ['backward', 'left', 'down'],
        '右前下': ['right', 'forward', 'down'],
        '左前下': ['left', 'forward', 'down'],
        '右后下': ['right', 'backward', 'down'],
        '左后下': ['left', 'backward', 'down'],
        '上前右': ['up', 'forward', 'right'],
        '下前右': ['down', 'forward', 'right'],
    }
    
    # 实际运动方向（ENU坐标系）
    actual_direction = motion_info['total_displacement']
    actual_forward = math.sqrt(actual_direction[0]**2 + actual_direction[1]**2)  # 水平前进
    actual_right = 0  # 假设没有明显的左右运动
    actual_down = -actual_direction[2]  # 向下运动
    
    print(f"\n实际运动特征:")
    print(f"  前进方向: {actual_forward:.2f}m")
    print(f"  右侧方向: {actual_right:.2f}m") 
    print(f"  向下方向: {actual_down:.2f}m")
    
    # 分析每个轴的加速度特征
    acc_x_mean = np.mean([d['acc_x'] for d in data])
    acc_y_mean = np.mean([d['acc_y'] for d in data])
    acc_z_mean = np.mean([d['acc_z'] for d in data])
    
    acc_x_std = np.std([d['acc_x'] for d in data])
    acc_y_std = np.std([d['acc_y'] for d in data])
    acc_z_std = np.std([d['acc_z'] for d in data])
    
    print(f"\nIMU加速度统计:")
    print(f"  X轴: 均值={acc_x_mean:.3f}g, 标准差={acc_x_std:.3f}g")
    print(f"  Y轴: 均值={acc_y_mean:.3f}g, 标准差={acc_y_std:.3f}g")
    print(f"  Z轴: 均值={acc_z_mean:.3f}g, 标准差={acc_z_std:.3f}g")
    
    # 分析重力分量
    # 重力应该主要体现在"下"方向的轴上
    gravity_candidates = []
    if abs(acc_x_mean) > 0.3:  # 如果X轴有明显的重力分量
        gravity_candidates.append(('X', acc_x_mean))
    if abs(acc_y_mean) > 0.3:  # 如果Y轴有明显的重力分量
        gravity_candidates.append(('Y', acc_y_mean))
    if abs(acc_z_mean) > 0.3:  # 如果Z轴有明显的重力分量
        gravity_candidates.append(('Z', acc_z_mean))
    
    print(f"\n重力分量分析:")
    for axis, value in gravity_candidates:
        direction = "向下" if value > 0 else "向上"
        print(f"  {axis}轴可能指向{direction} (重力分量: {value:.3f}g)")
    
    # 基于运动特征和重力分量推断坐标系
    print(f"\n坐标系推断:")
    
    # 找出最可能的"下"轴（重力分量最大的轴）
    max_gravity_axis = None
    max_gravity_value = 0
    for axis, value in gravity_candidates:
        if abs(value) > abs(max_gravity_value):
            max_gravity_axis = axis
            max_gravity_value = value
    
    if max_gravity_axis:
        down_direction = "正方向" if max_gravity_value > 0 else "负方向"
        print(f"  {max_gravity_axis}轴最可能指向下方 ({down_direction})")
    
    # 分析运动方向对应的轴
    # 假设前进方向对应加速度变化最大的轴
    motion_candidates = []
    if acc_x_std > 0.1:
        motion_candidates.append(('X', acc_x_std))
    if acc_y_std > 0.1:
        motion_candidates.append(('Y', acc_y_std))
    if acc_z_std > 0.1:
        motion_candidates.append(('Z', acc_z_std))
    
    motion_candidates.sort(key=lambda x: x[1], reverse=True)
    
    print(f"\n运动轴分析 (按变化程度排序):")
    for i, (axis, std_val) in enumerate(motion_candidates):
        if i == 0:
            print(f"  {axis}轴最可能对应主运动方向 (标准差: {std_val:.3f}g)")
        else:
            print(f"  {axis}轴 (标准差: {std_val:.3f}g)")
    
    # 给出最可能的坐标系配置
    if max_gravity_axis and motion_candidates:
        main_motion_axis = motion_candidates[0][0]
        
        print(f"\n最可能的坐标系配置:")
        if max_gravity_axis == 'Z' and max_gravity_value > 0:
            if main_motion_axis == 'X':
                print(f"  推测: X轴=前进, Y轴=左右, Z轴=向下 (前右下或前左下)")
            elif main_motion_axis == 'Y':
                print(f"  推测: X轴=左右, Y轴=前进, Z轴=向下 (右前下或左前下)")
        elif max_gravity_axis == 'Z' and max_gravity_value < 0:
            if main_motion_axis == 'X':
                print(f"  推测: X轴=前进, Y轴=左右, Z轴=向上 (前右上或前左上)")
            elif main_motion_axis == 'Y':
                print(f"  推测: X轴=左右, Y轴=前进, Z轴=向上 (右前上或左前上)")
        elif max_gravity_axis == 'X':
            if max_gravity_value > 0:
                print(f"  推测: X轴=向下, Y轴和Z轴为水平方向")
            else:
                print(f"  推测: X轴=向上, Y轴和Z轴为水平方向")
        elif max_gravity_axis == 'Y':
            if max_gravity_value > 0:
                print(f"  推测: Y轴=向下, X轴和Z轴为水平方向")
            else:
                print(f"  推测: Y轴=向上, X轴和Z轴为水平方向")

def visualize_data(data):
    """
    可视化数据
    """
    if len(data) < 2:
        return
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # GPS轨迹
    x_enu = [d['x_enu'] for d in data]
    y_enu = [d['y_enu'] for d in data]
    z_enu = [d['z_enu'] for d in data]
    
    axes[0,0].plot(x_enu, y_enu, 'b-o', markersize=2)
    axes[0,0].set_xlabel('东向位移 (m)')
    axes[0,0].set_ylabel('北向位移 (m)')
    axes[0,0].set_title('水平轨迹 (俯视图)')
    axes[0,0].grid(True)
    axes[0,0].axis('equal')
    
    # 高度变化
    time_steps = list(range(len(data)))
    axes[0,1].plot(time_steps, z_enu, 'g-o', markersize=2)
    axes[0,1].set_xlabel('时间步')
    axes[0,1].set_ylabel('高度变化 (m)')
    axes[0,1].set_title('高度变化')
    axes[0,1].grid(True)
    
    # 加速度数据
    acc_x = [d['acc_x'] for d in data]
    acc_y = [d['acc_y'] for d in data]
    acc_z = [d['acc_z'] for d in data]
    
    axes[1,0].plot(time_steps, acc_x, 'r-', label='X轴', linewidth=1)
    axes[1,0].plot(time_steps, acc_y, 'g-', label='Y轴', linewidth=1)
    axes[1,0].plot(time_steps, acc_z, 'b-', label='Z轴', linewidth=1)
    axes[1,0].set_xlabel('时间步')
    axes[1,0].set_ylabel('加速度 (g)')
    axes[1,0].set_title('IMU加速度数据')
    axes[1,0].legend()
    axes[1,0].grid(True)
    
    # 3D轨迹
    ax_3d = fig.add_subplot(2, 2, 4, projection='3d')
    ax_3d.plot(x_enu, y_enu, z_enu, 'b-o', markersize=2)
    ax_3d.set_xlabel('东向 (m)')
    ax_3d.set_ylabel('北向 (m)')
    ax_3d.set_zlabel('高度 (m)')
    ax_3d.set_title('3D轨迹')
    
    plt.tight_layout()
    plt.savefig('imu_coordinate_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """
    主函数
    """
    print("=" * 60)
    print("IMU坐标系估计分析")
    print("=" * 60)
    
    # 加载数据
    file_path = "Datasets/dataset18/mav0/imu0/Video00013.txt"
    print(f"加载数据文件: {file_path}")
    
    data = load_imu_data(file_path)
    if not data:
        print("错误：无法加载数据")
        return
    
    print(f"成功加载 {len(data)} 条数据记录")
    
    # 转换GPS坐标
    data = convert_gps_to_meters(data)
    
    # 分析运动方向
    motion_info = analyze_motion_direction(data)
    
    # 测试坐标系配置
    test_coordinate_systems(data, motion_info)
    
    # 可视化
    print(f"\n生成可视化图表...")
    visualize_data(data)
    
    print("=" * 60)

if __name__ == "__main__":
    main()
