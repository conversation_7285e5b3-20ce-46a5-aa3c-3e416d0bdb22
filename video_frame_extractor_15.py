import cv2
import os
import csv
from pathlib import Path

def get_next_dataset_number():
    """获取下一个可用的数据集编号"""
    datasets_dir = Path("Datasets")
    if not datasets_dir.exists():
        datasets_dir.mkdir(parents=True, exist_ok=True)
        return 1
    
    existing_datasets = []
    for item in datasets_dir.iterdir():
        if item.is_dir() and item.name.startswith("dataset"):
            try:
                num = int(item.name.replace("dataset", ""))
                existing_datasets.append(num)
            except ValueError:
                continue
    
    if not existing_datasets:
        return 1
    else:
        return max(existing_datasets) + 1

def create_directory_structure(dataset_num):
    """创建数据集目录结构"""
    base_path = Path(f"Datasets/dataset{dataset_num}/mav0/cam0")
    data_path = base_path / "data"
    
    # 创建目录
    data_path.mkdir(parents=True, exist_ok=True)
    
    return base_path, data_path

def extract_frames_at_15hz(video_path, output_data_dir):
    """按15Hz频率提取视频帧"""
    # 打开视频文件
    cap = cv2.VideoCapture(video_path)
    
    if not cap.isOpened():
        raise ValueError(f"无法打开视频文件: {video_path}")
    
    # 获取视频信息
    fps = cap.get(cv2.CAP_PROP_FPS)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    duration = total_frames / fps
    
    print(f"视频信息:")
    print(f"  原始帧率: {fps} FPS")
    print(f"  总帧数: {total_frames}")
    print(f"  视频时长: {duration:.2f} 秒")
    
    # 计算15Hz提取的参数
    target_fps = 15
    frame_interval = fps / target_fps  # 每隔多少帧提取一次
    timestamp_interval_ns = 66666667  # 66.67ms = 66,666,667 ns
    
    timestamps = []
    frame_count = 0
    # 设置起始时间戳
    start_timestamp = 1403636579763555584
    current_timestamp = start_timestamp
    
    print(f"开始按15Hz频率提取帧...")
    
    while True:
        # 计算当前应该读取的帧位置
        target_frame = int(frame_count * frame_interval)
        
        # 设置视频位置到目标帧
        cap.set(cv2.CAP_PROP_POS_FRAMES, target_frame)
        
        ret, frame = cap.read()
        if not ret:
            break
        
        # 格式化时间戳为19位数字（前面补零）
        formatted_timestamp = f"{current_timestamp:019d}"

        # 保存帧为PNG文件
        filename = f"{formatted_timestamp}.png"
        output_path = output_data_dir / filename
        cv2.imwrite(str(output_path), frame)

        # 记录时间戳（保存实际的时间戳值和文件名）
        timestamps.append((current_timestamp, filename))
        
        print(f"  提取帧 {frame_count + 1}: {filename}")
        
        # 更新计数器和时间戳
        frame_count += 1
        current_timestamp += timestamp_interval_ns
        
        # 检查是否超出视频长度
        if target_frame >= total_frames - 1:
            break
    
    cap.release()
    print(f"完成帧提取，共提取 {len(timestamps)} 帧")
    
    return timestamps

def generate_csv_file(timestamps, output_dir):
    """生成data.csv文件"""
    csv_path = output_dir / "data.csv"
    
    with open(csv_path, 'w', newline='\n', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        # 写入标题行
        writer.writerow(['#timestamp [ns]', 'filename'])
        # 写入数据
        for timestamp, filename in timestamps:
            # 格式化时间戳为19位数字（前面补零）
            formatted_timestamp = f"{timestamp:019d}"
            writer.writerow([formatted_timestamp, filename])
    
    print(f"生成CSV文件: {csv_path}")

def generate_txt_file(timestamps, output_dir, dataset_num):
    """生成datasetN.txt文件"""
    txt_path = output_dir / f"dataset{dataset_num}.txt"

    with open(txt_path, 'w', encoding='utf-8', newline='\n') as txtfile:
        for timestamp, _ in timestamps:
            # 格式化时间戳为19位数字（前面补零）
            formatted_timestamp = f"{timestamp:019d}"
            txtfile.write(f"{formatted_timestamp}\n")

    print(f"生成TXT文件: {txt_path}")

def main():
    # 在这里指定要处理的视频文件路径
    #video_file_path = "Video/long_focal_length.mp4"  #dataset1
    #video_file_path = "Video/short_focal_length.mp4"  #dataset2
    #video_file_path = "Video/long_focal_length_slow.mp4"  #dataset3
    #video_file_path = "Video/long_focal_length_slow1.mp4"  #dataset4
    #video_file_path = "Video/video_test1.mp4"  #dataset5
    #video_file_path = "Video/video_test2.mp4"  #dataset6
    #video_file_path = "Video/video_5_long.mp4"  #dataset7
    #video_file_path = "Video/video_49_long.mp4"  #dataset8
    #video_file_path = "Video/video_49_short_1_crop.mp4"  #dataset9
    #video_file_path = "Video/video_49_short_1_crop.mp4"  #dataset10
    #video_file_path = "Video/video_49_short_1_crop_offset.mp4"  #dataset11
    #video_file_path = "Video/video_with_imu.mp4"  #dataset17
    video_file_path = "Video/video_with_imu_1.mp4"  #dataset18

    print(f"开始处理视频文件: {video_file_path}")
    
    # 检查视频文件是否存在
    if not os.path.exists(video_file_path):
        print(f"错误: 视频文件不存在: {video_file_path}")
        return
    
    try:
        # 获取下一个数据集编号
        dataset_num = get_next_dataset_number()
        print(f"创建数据集: dataset{dataset_num}")
        
        # 创建目录结构
        base_dir, data_dir = create_directory_structure(dataset_num)
        print(f"创建目录结构: {base_dir}")
        
        # 提取视频帧
        timestamps = extract_frames_at_15hz(video_file_path, data_dir)
        
        # 生成CSV文件
        generate_csv_file(timestamps, base_dir)
        
        # 生成TXT文件
        generate_txt_file(timestamps, base_dir, dataset_num)
        
        print(f"\n处理完成!")
        print(f"数据集位置: {base_dir}")
        print(f"提取的帧数: {len(timestamps)}")
        
    except Exception as e:
        print(f"处理过程中发生错误: {e}")

if __name__ == "__main__":
    main()
